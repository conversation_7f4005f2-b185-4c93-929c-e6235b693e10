{"name": "attractivescore-ai", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@clerk/nextjs": "^6.9.6", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.4", "@radix-ui/react-tooltip": "^1.1.2", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/ssr": "^0.5.1", "@supabase/supabase-js": "^2.47.10", "@trpc/client": "^10.45.2", "@trpc/react-query": "^10.45.2", "@trpc/server": "^10.45.2", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "framer-motion": "^11.3.30", "lucide-react": "^0.436.0", "next": "^15.1.3", "next-themes": "^0.3.0", "react": "^18", "react-dom": "^18", "react-icons": "^5.4.0", "sonner": "^1.7.1", "superjson": "^2.2.1", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "zod": "^3.23.8"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.2.6", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}