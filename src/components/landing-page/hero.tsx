import Image from "next/image";
import { Button } from "../ui/button";
import { SignUpButton } from "@clerk/nextjs";

export default function Hero() {
  return (
    <section className="relative min-h-screen flex items-center justify-center pt-16">
      <div className="container grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
        <div className="flex flex-col gap-6 text-left">
          <div className="space-y-4">
            <div className="inline-block rounded-lg bg-muted px-3 py-1 text-sm">
              ✨ AI-Powered Analysis
            </div>
            <h1 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl lg:text-6xl hero-title">
              <span className="bg-gradient-to-r from-violet-500 via-pink-500 to-yellow-500 bg-clip-text text-transparent animate-gradient">
              Find Out Your Attractiveness Score in Seconds!
              </span>
            </h1>
            <p className="text-lg md:text-xl text-muted-foreground mt-6">
              Get instant, AI-powered insights into your visual appeal
            </p>
          </div>
          <div className="flex gap-4">
            <SignUpButton>
              <Button size="lg">Try Free Analysis</Button>
            </SignUpButton>
          </div>
        </div>
        <div className="relative">
          <div className="absolute -inset-4 bg-gradient-to-r from-pink-500 to-purple-500 rounded-xl opacity-30 blur-xl"></div>
          <Image
            src="/hero.jpg"
            alt="Attractive Score Demo"
            width={1200}
            height={800}
            className="relative rounded-lg shadow-xl"
            priority
          />
        </div>
      </div>
    </section>
  );
}
