import {
  Accordion,
  Accordion<PERSON>ontent,
  Accordion<PERSON><PERSON>,
  AccordionTrigger,
} from "../ui/accordion";

const Faq = () => {
  const faqs = [
    {
      question: "What factors does the AI consider in the attractiveness analysis?",
      answer:
        "Our AI analyzes multiple facial features including facial symmetry, golden ratio proportions, skin texture, facial harmony, and smile aesthetics. It also considers universal beauty markers backed by scientific research while being sensitive to diverse beauty standards across different cultures.",
    },
    {
      question: "How long does it take to get my attractiveness score?",
      answer:
        "The analysis is nearly instantaneous! Once you upload your photo, our AI processes it within seconds to provide you with a detailed score breakdown and personalized recommendations. You can analyze multiple photos quickly to understand how different angles and expressions affect your score.",
    },
    {
      question: "How does the AI attractiveness rating system work?",
      answer:
        "Our AI system analyzes various facial features using advanced computer vision technology. It considers factors like facial symmetry, proportions, and other scientifically studied attributes of attractiveness. The system provides objective feedback based on these universal beauty standards while maintaining a supportive and constructive approach.",
    },
    {
      question: "Is the rating system biased towards certain ethnicities or features?",
      answer:
        "No, our AI has been trained on a diverse dataset representing people from all backgrounds. It recognizes and appreciates beauty across different ethnicities, ages, and facial features. We continuously update our models to ensure fair and unbiased analysis for everyone.",
    },
    {
      question: "How accurate are the AI ratings?",
      answer:
        "Our AI provides consistent and objective analysis based on established beauty standards and facial symmetry research. However, it&apos;s important to remember that beauty is subjective, and our ratings should be viewed as one perspective among many. The goal is to provide insights for self-improvement while promoting healthy self-image.",
    },
    {
      question: "How can I improve my attractiveness score?",
      answer:
        "Our system provides personalized recommendations based on your analysis. These might include tips about optimal photo angles, lighting, facial expressions, and grooming suggestions. Remember, confidence and self-care often contribute significantly to attractiveness.",
    },
    {
      question: "Is my data private and secure?",
      answer:
        "Yes, we take your privacy very seriously. The images are deleted after analysis, and they are not stored in our database. We never share your photos or personal data with third parties. You can read more about our security measures in our privacy policy.",
    },
    {
      question: "How often can I get new ratings?",
      answer:
        "Depending on your subscription plan, you can submit multiple photos for analysis. We recommend taking photos in different settings and angles to get comprehensive feedback. Each plan comes with a set number of monthly analyses, and you can upgrade anytime for additional ratings.",
    },
  ];

  return (
    <section id="faq" className="py-32">
      <div className="container">
        <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl text-center">
          Frequently Asked Questions
        </h2>
        {faqs.map((faq, index) => (
          <Accordion key={index} type="single" collapsible>
            <AccordionItem value={`item-${index}`}>
              <AccordionTrigger className="hover:text-foreground/60 hover:no-underline text-lg">
                {faq.question}
              </AccordionTrigger>
              <AccordionContent className="text-normal">
                {faq.answer}
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        ))}
      </div>
    </section>
  );
};

export default Faq;
