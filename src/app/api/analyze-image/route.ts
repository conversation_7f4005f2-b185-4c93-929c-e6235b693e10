import { NextRequest, NextResponse } from "next/server";
import { getAuth } from "@clerk/nextjs/server";
import { PROMPTS } from '../../../lib/prompts';
// Function to check if user is paid (implement your logic here)
async function isUserPaid(userId: string): Promise<boolean> {
  // TODO: Implement your paid user check logic
  // This could involve checking Clerk metadata, a subscription database, etc.
  return true; // Temporary: return true for testing
}

// Function to extract JSO<PERSON> from markdown code blocks if present
function extractJsonFromMarkdown(content: string): string {
  // Try to extract JSON from markdown code blocks
  const jsonMatch = content.match(/```(?:json)?\s*(\{[\s\S]*?\})\s*```/);
  if (jsonMatch && jsonMatch[1]) {
    return jsonMatch[1];
  }
  // If no markdown blocks found, return the original content
  return content;
}

export async function POST(req: NextRequest) {
  try {
    const { userId } = await getAuth(req);
    if (!userId) {
      console.log('Unauthorized: No userId found');
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const formData = await req.formData();
    const image = formData.get("image") as File;
    const analysisType = formData.get("analysisType") as string || "attractive-score";

      const promptMap = {
        "attractive-score": PROMPTS.attractiveScore,
        "smile-rating": PROMPTS.smileAnalysis
      };

    if (!image) {
      console.log('No image provided in request');
      return NextResponse.json({ error: "No image provided" }, { status: 400 });
    }

    console.log('Image received:', {
      name: image.name,
      type: image.type,
      size: image.size
    });

    // Convert image to base64
    const bytes = await image.arrayBuffer();
    const base64Image = Buffer.from(bytes).toString('base64');
    const imageDataUrl = `data:${image.type};base64,${base64Image}`;

    // Check if OpenRouter API key is configured
    const openRouterApiKey = process.env.OPENROUTER_API_KEY;
    if (!openRouterApiKey) {
      console.error('OpenRouter API key not configured');
      return NextResponse.json({ error: "Service misconfigured" }, { status: 500 });
    }

    // Call OpenRouter Vision AI API
    console.log('Sending request to OpenRouter...');
    try {
      const headers = new Headers();
      headers.append('Authorization', `Bearer ${openRouterApiKey}`);
      headers.append('Content-Type', 'application/json');

      const validAnalysisTypes = ["attractive-score", "smile-rating"] as const;
      type AnalysisType = typeof validAnalysisTypes[number];
      
      if (!validAnalysisTypes.includes(analysisType as AnalysisType)) {
        return NextResponse.json({ error: "Invalid analysis type" }, { status: 400 });
      }

      const openRouterResponse = await fetch('https://openrouter.ai/api/v1/chat/completions', {
        method: 'POST',
        headers: headers,
        body: JSON.stringify({
          model: 'google/gemini-flash-1.5-8b',
          messages: [
            {
              role: 'user',
              content: [
                {
                  type: 'text',
                  text: promptMap[analysisType as AnalysisType]
                },
                {
                  type: 'image_url',
                  image_url: imageDataUrl
                }
              ]
            }
          ]
        })
      });

      if (!openRouterResponse.ok) {
        const responseText = await openRouterResponse.text();
        console.error('OpenRouter API error:', responseText);
        return NextResponse.json({ 
          error: 'Failed to analyze image',
          details: responseText 
        }, { status: openRouterResponse.status });
      }

      const aiResponse = await openRouterResponse.json();

      if (!aiResponse.choices?.[0]?.message?.content) {
        console.error('Unexpected API response format:', aiResponse);
        return NextResponse.json({ 
          error: 'Invalid API response format',
          details: aiResponse 
        }, { status: 500 });
      }

      const content = aiResponse.choices[0].message.content;
      console.log('Raw AI response:', content);

      // Check if the response indicates no face was found
      if (content.includes("The image doesn't show a face") || 
          content.includes("No facial structure is visible") ||
          content.includes("The image does not show any facial features")) {
        return NextResponse.json({ 
          error: 'No human face detected in the image. Please upload a clear photo of a face.'
        }, { status: 400 });
      }


      let analysisResult;
      try {
        // Try to parse the content directly first
        try {
          analysisResult = JSON.parse(content);
        } catch {
          // If direct parsing fails, try to extract JSON from markdown
          const extractedJson = extractJsonFromMarkdown(content);
          analysisResult = JSON.parse(extractedJson);
        }

        // Check if the AI detected no human face
        if (analysisResult.error) {
          return NextResponse.json({ 
            error: analysisResult.error
          }, { status: 400 });
        }

      } catch (error) {
        console.error('Failed to parse AI response content:', error);
        return NextResponse.json({ 
          error: 'Failed to parse analysis result',
          details: content
        }, { status: 500 });
      }

      // TODO: Implement cloud storage for paid users
      console.log('Analysis complete');

      return NextResponse.json(analysisResult);
    } catch (error) {
      console.error("OpenRouter API error:", error);
      return NextResponse.json({ 
        error: "Failed to connect to OpenRouter API",
        details: error instanceof Error ? error.message : String(error)
      }, { status: 500 });
    }
  } catch (error) {
    console.error("Server error:", error);
    return NextResponse.json({ 
      error: "Internal server error",
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}
